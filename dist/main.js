#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const zudelloApiService_js_1 = require("./services/zudelloApiService.js");
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
// Validate required environment variables
const requiredEnvVars = [
    'ZUDELLO_API_URL',
    'ZUDELLO_AUTH_TOKEN',
    'ZUDELLO_ORG_UUID',
    'ZUDELLO_TEAM_UUID'
];
console.log('🔍 Validating environment variables...');
for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
        console.error(`❌ Missing required environment variable: ${envVar}`);
        process.exit(1);
    }
    console.log(`✅ ${envVar}: ${envVar.includes('TOKEN') ? '[REDACTED]' : process.env[envVar]}`);
}
// Initialize Zudello API service
const zudelloConfig = {
    baseUrl: process.env.ZUDELLO_API_URL,
    authToken: process.env.ZUDELLO_AUTH_TOKEN,
    organizationUuid: process.env.ZUDELLO_ORG_UUID,
    teamUuid: process.env.ZUDELLO_TEAM_UUID
};
const zudelloService = new zudelloApiService_js_1.ZudelloApiService(zudelloConfig);
// Create MCP server
const server = new index_js_1.Server({
    name: 'zudello-mcp-server',
    version: '1.0.0',
}, {
    capabilities: {
        resources: {},
        tools: {},
    },
});
// List available resources
server.setRequestHandler(types_js_1.ListResourcesRequestSchema, async () => {
    return {
        resources: [
            {
                uri: 'zudello://resources',
                mimeType: 'application/json',
                name: 'Zudello Resources',
                description: 'Access to Zudello resources (document types, modules, etc.)',
            },
            {
                uri: 'zudello://documents',
                mimeType: 'application/json',
                name: 'Zudello Documents',
                description: 'Access to Zudello documents and data',
            },
        ],
    };
});
// Read resource content
server.setRequestHandler(types_js_1.ReadResourceRequestSchema, async (request) => {
    const { uri } = request.params;
    if (uri === 'zudello://resources') {
        const result = await zudelloService.getResourcesContext('general resources query');
        return {
            contents: [
                {
                    uri,
                    mimeType: 'application/json',
                    text: result.content,
                },
            ],
        };
    }
    if (uri === 'zudello://documents') {
        const result = await zudelloService.getDocumentsContext('general documents query');
        return {
            contents: [
                {
                    uri,
                    mimeType: 'application/json',
                    text: result.content,
                },
            ],
        };
    }
    throw new types_js_1.McpError(types_js_1.ErrorCode.InvalidRequest, `Unknown resource: ${uri}`);
});
// List available tools
server.setRequestHandler(types_js_1.ListToolsRequestSchema, async () => {
    return {
        tools: [
            {
                name: 'get_zudello_resources',
                description: 'Query Zudello resources (document types, modules, submodules)',
                inputSchema: {
                    type: 'object',
                    properties: {
                        query: {
                            type: 'string',
                            description: 'Query string to filter resources (e.g., "invoice", "purchase", "sales")',
                        },
                        module: {
                            type: 'string',
                            description: 'Optional module filter (e.g., PURCHASING, SALES, INVENTORY)',
                        },
                        submodule: {
                            type: 'string',
                            description: 'Optional submodule filter (e.g., INVOICE, ORDER)',
                        },
                    },
                    required: ['query'],
                },
            },
            {
                name: 'get_zudello_documents',
                description: 'Query Zudello documents and data',
                inputSchema: {
                    type: 'object',
                    properties: {
                        query: {
                            type: 'string',
                            description: 'Query string to filter documents (e.g., "recent", "invoice")',
                        },
                        limit: {
                            type: 'string',
                            description: 'Number of documents to return (default: 10)',
                        },
                    },
                    required: ['query'],
                },
            },
            {
                name: 'test_zudello_connection',
                description: 'Test the connection to Zudello API',
                inputSchema: {
                    type: 'object',
                    properties: {},
                },
            },
        ],
    };
});
// Handle tool calls
server.setRequestHandler(types_js_1.CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;
    switch (name) {
        case 'get_zudello_resources': {
            const query = args?.query || '';
            const result = await zudelloService.getResourcesContext(query);
            return {
                content: [
                    {
                        type: 'text',
                        text: result.content,
                    },
                ],
            };
        }
        case 'get_zudello_documents': {
            const query = args?.query || '';
            const result = await zudelloService.getDocumentsContext(query);
            return {
                content: [
                    {
                        type: 'text',
                        text: result.content,
                    },
                ],
            };
        }
        case 'test_zudello_connection': {
            const isConnected = await zudelloService.testConnection();
            return {
                content: [
                    {
                        type: 'text',
                        text: JSON.stringify({
                            connected: isConnected,
                            timestamp: new Date().toISOString(),
                            message: isConnected ? 'Successfully connected to Zudello API' : 'Failed to connect to Zudello API'
                        }, null, 2),
                    },
                ],
            };
        }
        default:
            throw new types_js_1.McpError(types_js_1.ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
    }
});
// Start the server
async function main() {
    console.log('🚀 Starting Zudello MCP Server...');
    // Test connection on startup
    const isConnected = await zudelloService.testConnection();
    if (!isConnected) {
        console.warn('⚠️  Warning: Could not establish connection to Zudello API on startup');
    }
    const transport = new stdio_js_1.StdioServerTransport();
    await server.connect(transport);
    console.log('✅ Zudello MCP Server is running and ready to accept connections');
}
// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down Zudello MCP Server...');
    await server.close();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    console.log('\n🛑 Shutting down Zudello MCP Server...');
    await server.close();
    process.exit(0);
});
// Start the server
main().catch((error) => {
    console.error('❌ Failed to start Zudello MCP Server:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map