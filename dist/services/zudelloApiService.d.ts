export interface ZudelloApiConfig {
    baseUrl: string;
    authToken: string;
    organizationUuid: string;
    teamUuid: string;
}
export interface ZudelloResource {
    id: string;
    name: string;
    type: string;
    module?: string;
    submodule?: string;
    [key: string]: any;
}
export interface ZudelloDocument {
    id: string;
    name: string;
    type: string;
    created_at: string;
    updated_at: string;
    [key: string]: any;
}
export interface ZudelloApiResponse<T> {
    data: T[];
    count: number;
    next?: string;
    previous?: string;
}
export declare class ZudelloApiService {
    private client;
    private config;
    constructor(config: ZudelloApiConfig);
    getResourcesContext(query: string): Promise<{
        type: string;
        content: string;
    }>;
    getDocumentsContext(query: string): Promise<{
        type: string;
        content: string;
    }>;
    testConnection(): Promise<boolean>;
}
//# sourceMappingURL=zudelloApiService.d.ts.map