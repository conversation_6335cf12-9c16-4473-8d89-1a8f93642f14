"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZudelloApiService = void 0;
const axios_1 = __importDefault(require("axios"));
class ZudelloApiService {
    constructor(config) {
        this.config = config;
        this.client = axios_1.default.create({
            baseURL: config.baseUrl,
            headers: {
                'Authorization': `Bearer ${config.authToken}`,
                'Content-Type': 'application/json',
                'x-organization': config.organizationUuid,
                'x-team': config.teamUuid,
                'accept': 'application/json, text/plain, */*'
            },
            timeout: 30000
        });
        // Add request interceptor for logging
        this.client.interceptors.request.use((config) => {
            console.log(`Making request to: ${config.method?.toUpperCase()} ${config.url}`);
            return config;
        }, (error) => {
            console.error('Request error:', error);
            return Promise.reject(error);
        });
        // Add response interceptor for error handling
        this.client.interceptors.response.use((response) => response, (error) => {
            console.error('API Error:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                url: error.config?.url
            });
            return Promise.reject(error);
        });
    }
    async getResourcesContext(query) {
        try {
            console.log(`Getting resources context for query: "${query}"`);
            // Parse query parameters from the input
            const params = {};
            // Default parameters
            params.resource_type = 'document_type';
            params.global_resource = 'false';
            params.team_uuid = this.config.teamUuid;
            // Add module/submodule based on query content
            if (query.toLowerCase().includes('invoice')) {
                params.module = 'PURCHASING';
                params.submodule = 'INVOICE';
            }
            else if (query.toLowerCase().includes('purchase')) {
                params.module = 'PURCHASING';
                params.submodule = 'ORDER';
            }
            else if (query.toLowerCase().includes('sales')) {
                params.module = 'SALES';
            }
            else if (query.toLowerCase().includes('inventory')) {
                params.module = 'INVENTORY';
            }
            console.log('Request parameters:', params);
            const response = await this.client.get('/general-store/api/v1/resources/', { params });
            const formattedResponse = {
                query: query,
                endpoint: '/general-store/api/v1/resources/',
                parameters: params,
                response: response.data,
                timestamp: new Date().toISOString()
            };
            return {
                type: 'text',
                content: JSON.stringify(formattedResponse, null, 2)
            };
        }
        catch (error) {
            console.error('Error fetching resources:', error);
            const errorResponse = {
                error: 'Failed to retrieve Zudello resources',
                query: query,
                details: error.response?.data || error.message,
                status: error.response?.status,
                timestamp: new Date().toISOString()
            };
            return {
                type: 'text',
                content: JSON.stringify(errorResponse, null, 2)
            };
        }
    }
    async getDocumentsContext(query) {
        try {
            console.log(`Getting documents context for query: "${query}"`);
            const params = {};
            // Add filters based on query content
            if (query.toLowerCase().includes('recent')) {
                params.sort = '-created_at';
                params.limit = '10';
            }
            if (query.toLowerCase().includes('invoice')) {
                params.document_type = 'invoice';
            }
            console.log('Request parameters:', params);
            const response = await this.client.get('/general-store/api/v1/documents/', { params });
            const formattedResponse = {
                query: query,
                endpoint: '/general-store/api/v1/documents/',
                parameters: params,
                response: response.data,
                timestamp: new Date().toISOString()
            };
            return {
                type: 'text',
                content: JSON.stringify(formattedResponse, null, 2)
            };
        }
        catch (error) {
            console.error('Error fetching documents:', error);
            const errorResponse = {
                error: 'Failed to retrieve Zudello documents',
                query: query,
                details: error.response?.data || error.message,
                status: error.response?.status,
                timestamp: new Date().toISOString()
            };
            return {
                type: 'text',
                content: JSON.stringify(errorResponse, null, 2)
            };
        }
    }
    async testConnection() {
        try {
            console.log('Testing Zudello API connection...');
            await this.client.get('/general-store/api/v1/resources/', {
                params: {
                    resource_type: 'document_type',
                    global_resource: 'false',
                    team_uuid: this.config.teamUuid,
                    limit: '1'
                }
            });
            console.log('✅ Zudello API connection successful');
            return true;
        }
        catch (error) {
            console.error('❌ Zudello API connection failed:', error.response?.data || error.message);
            return false;
        }
    }
}
exports.ZudelloApiService = ZudelloApiService;
//# sourceMappingURL=zudelloApiService.js.map