#!/usr/bin/env ts-node

import chalk from 'chalk';
import dotenv from 'dotenv';
import { ZudelloApiService } from '../src/services/zudelloApiService';
import { 
  TestRunner, 
  printFormattedResponse, 
  validateEnvironment, 
  createTestConfig,
  delay 
} from './utils/test-helpers';

// Load environment variables
dotenv.config();

interface TestCase {
  name: string;
  tool: string;
  args: Record<string, any>;
  expectedStatus?: 'success' | 'error';
  validate?: (result: any) => boolean;
}

class AutomatedTestSuite {
  private service: ZudelloApiService;
  private testRunner: TestRunner;

  constructor() {
    if (!validateEnvironment()) {
      process.exit(1);
    }

    this.service = new ZudelloApiService(createTestConfig());
    this.testRunner = new TestRunner();
  }

  async runAllTests(): Promise<void> {
    console.log(chalk.cyan('🤖 Zudello MCP Server Automated Test Suite'));
    console.log(chalk.gray('═'.repeat(60)));

    const testCases = this.getTestCases();

    for (const testCase of testCases) {
      await this.runTestCase(testCase);
      await delay(500); // Small delay between tests
    }

    this.testRunner.printSummary();
    
    const results = this.testRunner.getResults();
    const failedTests = results.filter(r => !r.passed);
    
    if (failedTests.length > 0) {
      process.exit(1);
    }
  }

  private getTestCases(): TestCase[] {
    return [
      // Connection tests
      {
        name: 'Basic connection test',
        tool: 'test_zudello_connection',
        args: {},
        expectedStatus: 'success',
        validate: (result) => result === true
      },

      // Resources tests - Valid queries
      {
        name: 'Resources: Invoice query',
        tool: 'get_zudello_resources',
        args: { query: 'invoice' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.module === 'PURCHASING' && 
                   parsed.parameters?.submodule === 'INVOICE';
          } catch { return false; }
        }
      },

      {
        name: 'Resources: Purchase query',
        tool: 'get_zudello_resources',
        args: { query: 'purchase' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.module === 'PURCHASING' && 
                   parsed.parameters?.submodule === 'ORDER';
          } catch { return false; }
        }
      },

      {
        name: 'Resources: Sales query',
        tool: 'get_zudello_resources',
        args: { query: 'sales' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.module === 'SALES';
          } catch { return false; }
        }
      },

      {
        name: 'Resources: Inventory query',
        tool: 'get_zudello_resources',
        args: { query: 'inventory' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.module === 'INVENTORY';
          } catch { return false; }
        }
      },

      // Edge cases
      {
        name: 'Resources: Empty query',
        tool: 'get_zudello_resources',
        args: { query: '' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.resource_type === 'document_type';
          } catch { return false; }
        }
      },

      {
        name: 'Resources: Unknown query',
        tool: 'get_zudello_resources',
        args: { query: 'unknown_module_xyz' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.resource_type === 'document_type' &&
                   !parsed.parameters?.module;
          } catch { return false; }
        }
      },

      {
        name: 'Resources: Case insensitive query',
        tool: 'get_zudello_resources',
        args: { query: 'INVOICE' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.parameters?.module === 'PURCHASING' && 
                   parsed.parameters?.submodule === 'INVOICE';
          } catch { return false; }
        }
      },

      // Documents tests
      {
        name: 'Documents: Recent query',
        tool: 'get_zudello_documents',
        args: { query: 'recent' },
        expectedStatus: 'success', // May return error due to 404, but should handle gracefully
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.query === 'recent' && 
                   (parsed.response || parsed.error);
          } catch { return false; }
        }
      },

      {
        name: 'Documents: Invoice query',
        tool: 'get_zudello_documents',
        args: { query: 'invoice' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.query === 'invoice';
          } catch { return false; }
        }
      },

      {
        name: 'Documents: Empty query',
        tool: 'get_zudello_documents',
        args: { query: '' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            return parsed.query === '';
          } catch { return false; }
        }
      },

      // Parameter validation tests
      {
        name: 'Resources: Multiple keywords',
        tool: 'get_zudello_resources',
        args: { query: 'purchase invoice sales' },
        expectedStatus: 'success',
        validate: (result) => {
          try {
            const parsed = JSON.parse(result.content);
            // Should match the first keyword found in the if-else chain (invoice)
            return parsed.parameters?.module === 'PURCHASING' &&
                   parsed.parameters?.submodule === 'INVOICE';
          } catch { return false; }
        }
      }
    ];
  }

  private async runTestCase(testCase: TestCase): Promise<void> {
    this.testRunner.startTest(testCase.name);

    try {
      let result: any;

      switch (testCase.tool) {
        case 'test_zudello_connection':
          result = await this.service.testConnection();
          break;
        case 'get_zudello_resources':
          result = await this.service.getResourcesContext(testCase.args.query || '');
          break;
        case 'get_zudello_documents':
          result = await this.service.getDocumentsContext(testCase.args.query || '');
          break;
        default:
          throw new Error(`Unknown tool: ${testCase.tool}`);
      }

      // Validate result if validator provided
      if (testCase.validate) {
        const isValid = testCase.validate(result);
        if (!isValid) {
          throw new Error('Validation failed');
        }
      }

      this.testRunner.passTest(testCase.name, result);

      // Show detailed output for failed API calls that we expect to handle gracefully
      if (testCase.tool === 'get_zudello_documents' && typeof result === 'object' && result.content) {
        try {
          const parsed = JSON.parse(result.content);
          if (parsed.error) {
            console.log(chalk.yellow(`   ⚠️  API returned error (handled gracefully): ${parsed.error}`));
          }
        } catch {}
      }

    } catch (error: any) {
      this.testRunner.failTest(testCase.name, error.message);
    }
  }
}

// Run automated tests
if (require.main === module) {
  const testSuite = new AutomatedTestSuite();
  testSuite.runAllTests().catch(console.error);
}
