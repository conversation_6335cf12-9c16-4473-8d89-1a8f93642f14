# Zudello MCP Server Usage Guide

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your Zudello credentials
   ```

3. **Start the server:**
   ```bash
   npm run dev
   ```

## MCP Tools Available

### 1. get_zudello_resources
Queries Zudello resources (document types, modules, submodules).

**Example usage:**
```json
{
  "name": "get_zudello_resources",
  "arguments": {
    "query": "invoice"
  }
}
```

This will automatically set:
- `module: "PURCHASING"`
- `submodule: "INVOICE"`
- `resource_type: "document_type"`

### 2. get_zudello_documents
Queries Zudello documents (note: endpoint may return 404 depending on API availability).

**Example usage:**
```json
{
  "name": "get_zudello_documents",
  "arguments": {
    "query": "recent invoices"
  }
}
```

### 3. test_zudello_connection
Tests the connection to Zudello API.

**Example usage:**
```json
{
  "name": "test_zudello_connection",
  "arguments": {}
}
```

## MCP Resources

- `zudello://resources` - Access to Zudello resources
- `zudello://documents` - Access to Zudello documents

## API Endpoints Tested

✅ **Working:** `/general-store/api/v1/resources/`
- Successfully connects and returns data
- Supports filtering by module/submodule
- Returns document types and resource information

❓ **Unknown:** `/general-store/api/v1/documents/`
- Returns 404 (may require different authentication or parameters)
- Error handling implemented for graceful degradation

## Server Output Example

```
🔍 Validating environment variables...
✅ ZUDELLO_API_URL: https://api.au.3.zudello.io
✅ ZUDELLO_AUTH_TOKEN: [REDACTED]
✅ ZUDELLO_ORG_UUID: d49d898d-760b-4abe-ac27-b3a4a9880a71
✅ ZUDELLO_TEAM_UUID: 1907c60f-ebcd-4492-b3f2-6d0658a261c7
🚀 Starting Zudello MCP Server...
Testing Zudello API connection...
Making request to: GET /general-store/api/v1/resources/
✅ Zudello API connection successful
✅ Zudello MCP Server is running and ready to accept connections
```

## Integration with MCP Clients

This server follows the MCP protocol and can be integrated with any MCP-compatible client. The server communicates via stdio and provides both tools and resources for context retrieval.

## Security Notes

- All credentials are stored in environment variables
- API tokens are redacted in logs
- Proper error handling prevents credential leakage
- The `.env` file is excluded from version control
